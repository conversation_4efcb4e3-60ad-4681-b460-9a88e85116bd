package com.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Digital OSS配置助手类
 * 用于在静态方法中访问Spring管理的配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-30
 */
@Slf4j
@Component
public class DigitalOssConfigHelper implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext context) {
        applicationContext = context;
        log.info("DigitalOssConfigHelper已初始化，Spring上下文已设置");
    }
    
    /**
     * 检查是否启用了Digital模块的自定义OSS配置
     */
    public static boolean isCustomConfigEnabled() {
        try {
            if (applicationContext == null) {
                log.debug("Spring上下文未初始化，使用原有配置");
                return false;
            }
            
            // 尝试获取配置管理器
            Object configManager = applicationContext.getBean("digitalOssConfigManager");
            if (configManager == null) {
                log.debug("未找到DigitalOssConfigManager，使用原有配置");
                return false;
            }
            
            // 使用反射调用isCustomConfigEnabled方法
            Boolean enabled = (Boolean) configManager.getClass()
                .getMethod("isCustomConfigEnabled")
                .invoke(configManager);
            
            log.debug("Digital自定义OSS配置状态: {}", enabled);
            return Boolean.TRUE.equals(enabled);
            
        } catch (Exception e) {
            log.debug("检查Digital OSS配置状态时发生异常，使用原有配置: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取配置管理器
     */
    public static Object getConfigManager() {
        try {
            if (applicationContext == null) {
                return null;
            }
            return applicationContext.getBean("digitalOssConfigManager");
        } catch (Exception e) {
            log.debug("获取配置管理器失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取OSS客户端管理器
     */
    public static Object getClientManager() {
        try {
            if (applicationContext == null) {
                return null;
            }
            return applicationContext.getBean("digitalOssClientManager");
        } catch (Exception e) {
            log.debug("获取OSS客户端管理器失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取配置值（通用方法）
     */
    public static Object getConfigValue(String methodName) {
        try {
            Object configManager = getConfigManager();
            if (configManager == null) {
                return null;
            }
            
            return configManager.getClass()
                .getMethod(methodName)
                .invoke(configManager);
                
        } catch (Exception e) {
            log.debug("获取配置值失败 [{}]: {}", methodName, e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取OSS客户端实例（仅在启用自定义配置时）
     */
    public static Object getCustomOssClient() {
        try {
            if (!isCustomConfigEnabled()) {
                return null;
            }
            
            Object clientManager = getClientManager();
            if (clientManager == null) {
                return null;
            }
            
            return clientManager.getClass()
                .getMethod("getOssClient")
                .invoke(clientManager);
                
        } catch (Exception e) {
            log.error("获取自定义OSS客户端失败: {}", e.getMessage());
            return null;
        }
    }
}
