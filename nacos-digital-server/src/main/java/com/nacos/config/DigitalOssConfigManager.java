package com.nacos.config;

import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Digital模块OSS配置管理器
 * 提供统一的配置获取接口，支持动态切换配置源
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-30
 */
@Slf4j
@Component
public class DigitalOssConfigManager {
    
    @Autowired
    private DigitalOssProperties digitalOssProperties;
    
    @PostConstruct
    public void init() {
        log.info("Digital OSS配置管理器初始化完成");
        log.info("当前配置状态: {}", getConfigSummary());
    }
    
    /**
     * 检查是否启用了自定义配置
     */
    public boolean isCustomConfigEnabled() {
        return digitalOssProperties.isEnabled();
    }
    
    /**
     * 获取OSS端点
     */
    public String getEndpoint() {
        if (digitalOssProperties.isEnabled()) {
            log.debug("使用Digital自定义OSS配置 - endpoint: {}", digitalOssProperties.getEndpoint());
            return digitalOssProperties.getEndpoint();
        }
        return OssClientConfig.ENDPOINT;
    }
    
    /**
     * 获取访问密钥ID
     */
    public String getAccessKeyId() {
        if (digitalOssProperties.isEnabled()) {
            String keyId = digitalOssProperties.getAccessKeyId();
            log.debug("使用Digital自定义OSS配置 - accessKeyId: {}***", 
                keyId.length() > 8 ? keyId.substring(0, 8) : "***");
            return keyId;
        }
        return OssClientConfig.ACCESSKEYID;
    }
    
    /**
     * 获取访问密钥Secret
     */
    public String getSecretAccessKey() {
        if (digitalOssProperties.isEnabled()) {
            log.debug("使用Digital自定义OSS配置 - secretAccessKey: ***");
            return digitalOssProperties.getSecretAccessKey();
        }
        return OssClientConfig.SECRETACCESSKEY;
    }
    
    /**
     * 获取存储桶名称
     */
    public String getBucketName() {
        if (digitalOssProperties.isEnabled()) {
            log.debug("使用Digital自定义OSS配置 - bucketName: {}", digitalOssProperties.getBucketName());
            return digitalOssProperties.getBucketName();
        }
        return OssClientConfig.BUCKET_NAME;
    }
    
    /**
     * 获取访问路径前缀
     */
    public String getAccessPath() {
        if (digitalOssProperties.isEnabled()) {
            log.debug("使用Digital自定义OSS配置 - accessPath: {}", digitalOssProperties.getAccessPath());
            return digitalOssProperties.getAccessPath();
        }
        return OssClientConfig.ACCESS_PATH;
    }
    
    /**
     * 获取连接超时时间
     */
    public int getConnectionTimeout() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getConnectionTimeout();
        }
        return 30000; // 与原配置保持一致
    }
    
    /**
     * 获取读取超时时间
     */
    public int getSocketTimeout() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getSocketTimeout();
        }
        return 60000; // 与原配置保持一致
    }
    
    /**
     * 获取最大重试次数
     */
    public int getMaxErrorRetry() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getMaxErrorRetry();
        }
        return 5; // 与原配置保持一致
    }
    
    /**
     * 获取最大连接数
     */
    public int getMaxConnections() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getMaxConnections();
        }
        return 200; // 与原配置保持一致
    }
    
    /**
     * 获取空闲连接保留时间
     */
    public int getIdleConnectionTime() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getIdleConnectionTime();
        }
        return 300; // 与原配置保持一致
    }
    
    /**
     * 获取连接最大存活时间
     */
    public long getConnectionTTL() {
        if (digitalOssProperties.isEnabled()) {
            return digitalOssProperties.getConnectionTTL();
        }
        return 600000; // 与原配置保持一致
    }
    
    /**
     * 获取配置信息摘要（用于日志）
     */
    public String getConfigSummary() {
        if (digitalOssProperties.isEnabled()) {
            return String.format("Digital自定义OSS配置 [endpoint=%s, bucket=%s, accessPath=%s]", 
                getEndpoint(), getBucketName(), getAccessPath());
        } else {
            return "使用原有硬编码OSS配置";
        }
    }
    
    /**
     * 获取配置属性对象（用于其他组件）
     */
    public DigitalOssProperties getProperties() {
        return digitalOssProperties;
    }
}
