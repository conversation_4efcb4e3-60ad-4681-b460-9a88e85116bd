package com.nacos.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Digital模块专用OSS配置属性
 * 支持通过配置文件动态配置OSS参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "digital.oss")
public class DigitalOssProperties {
    
    /**
     * 是否启用自定义OSS配置
     * false: 使用原有硬编码配置（默认）
     * true: 使用配置文件中的自定义配置
     */
    private boolean enabled = false;
    
    /**
     * OSS服务端点
     * 默认值与原配置保持一致
     */
    private String endpoint = "https://oss-cn-beijing.aliyuncs.com";
    
    /**
     * OSS访问密钥ID
     * 默认值与原配置保持一致
     */
    private String accessKeyId = "LTAI5t9A9sRyNs1a2AsDT7Hp";
    
    /**
     * OSS访问密钥Secret
     * 默认值与原配置保持一致
     */
    private String secretAccessKey = "******************************";
    
    /**
     * OSS存储桶名称
     * 默认值与原配置保持一致
     */
    private String bucketName = "idotdesign";
    
    /**
     * OSS访问路径前缀
     * 默认值与原配置保持一致
     */
    private String accessPath = "https://cdn.diandiansheji.com/";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int socketTimeout = 60000;
    
    /**
     * 最大重试次数
     */
    private int maxErrorRetry = 5;
    
    /**
     * 最大连接数
     */
    private int maxConnections = 200;
    
    /**
     * 空闲连接保留时间（秒）
     */
    private int idleConnectionTime = 300;
    
    /**
     * 连接最大存活时间（毫秒）
     */
    private long connectionTTL = 600000;
}
