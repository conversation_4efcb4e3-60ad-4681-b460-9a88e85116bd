package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 视频翻译请求DTO（多服务商通用版本）
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@Schema(description = "视频翻译请求参数")
public class VideoTranslateRequestDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true, example = "user123")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 视频文件URL
     * 注意：在文件上传模式下，此字段可以为空，系统会自动设置上传后的文件URL
     */
    @Schema(description = "视频文件URL。" +
                         "URL模式：必填，提供视频文件的网络地址；" +
                         "文件上传模式：可选，如果同时提供file参数，此字段将被忽略",
            required = false,
            example = "https://example.com/video.mp4")
    @NotBlank(message = "视频文件URL不能为空")
    private String videoUrl;

    /**
     * 源语言代码
     */
    @Schema(description = "源语言代码", required = true, example = "cn")
    @NotBlank(message = "源语言不能为空")
    private String sourceLanguage;

    /**
     * 目标语言代码
     */
    @Schema(description = "目标语言代码", required = true, example = "en")
    @NotBlank(message = "目标语言不能为空")
    private String targetLanguage;

    /**
     * 音色ID（当useOriginalVoice为false时必填）
     */
    @Schema(description = "音色ID", required = false, example = "246")
    private String voiceId;

    /**
     * 任务名称（可选）
     */
    @Schema(description = "任务名称", required = false, example = "我的视频翻译任务")
    @Size(max = 100, message = "任务名称不能超过100个字符")
    private String taskName;

    /**
     * 是否使用原声（true-使用视频原声，false-使用声音库）
     */
    @Schema(description = "是否使用原声", required = false, example = "false")
    private Boolean useOriginalVoice = false;

    /**
     * 扩展参数（用于服务商特有参数，JSON格式）
     */
    @Schema(description = "扩展参数，用于服务商特有参数，JSON格式", required = false, example = "{\"customParam\":\"value\"}")
    private String extParams;

    // ==================== 业务方法 ====================

    /**
     * 验证源语言和目标语言是否相同
     */
    public boolean isSameLanguage() {
        return sourceLanguage != null && sourceLanguage.equals(targetLanguage);
    }

    /**
     * 获取语言对描述
     */
    public String getLanguagePairDescription() {
        return sourceLanguage + " → " + targetLanguage;
    }

    /**
     * 获取默认任务名称
     */
    public String getDefaultTaskName() {
        if (taskName != null && !taskName.trim().isEmpty()) {
            return taskName;
        }
        return "视频翻译任务 - " + getLanguagePairDescription();
    }

    /**
     * 验证voiceId是否必填
     */
    public boolean isVoiceIdRequired() {
        return useOriginalVoice == null || !useOriginalVoice;
    }

    /**
     * 验证参数完整性
     */
    public boolean isValid() {
        // 基础参数验证
        if (userId == null || userId.trim().isEmpty() ||
            videoUrl == null || videoUrl.trim().isEmpty() ||
            sourceLanguage == null || sourceLanguage.trim().isEmpty() ||
            targetLanguage == null || targetLanguage.trim().isEmpty()) {
            return false;
        }

        // 语言不能相同
        if (isSameLanguage()) {
            return false;
        }

        // 如果不使用原声，voiceId必填
        if (isVoiceIdRequired() && (voiceId == null || voiceId.trim().isEmpty())) {
            return false;
        }

        return true;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (useOriginalVoice == null) {
            useOriginalVoice = false;
        }
    }
}
