package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频翻译任务实体类（简化版）
 * 根据最新DDL结构优化，只保留必要字段，其他信息存储在JSON字段中
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("digital_video_translation_task")
@Schema(description = "视频翻译任务实体类")
public class VideoTranslateTaskPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一标识
     */
    @Schema(description = "任务唯一标识")
    @TableField("task_id")
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    /**
     * 服务商标识
     */
    @Schema(description = "服务商标识", example = "AZURE")
    @TableField("provider")
    private String provider;

    /**
     * 视频封面URL
     */
    @Schema(description = "视频封面URL")
    @TableField("cover_url")
    private String coverUrl;

    /**
     * 源视频URL
     */
    @Schema(description = "源视频URL")
    @TableField("source_video_url")
    private String sourceVideoUrl;

    /**
     * 翻译后的视频URL
     */
    @Schema(description = "翻译后的视频URL")
    @TableField("translated_video_url")
    private String translatedVideoUrl;

    /**
     * 翻译后的音频URL
     */
    @Schema(description = "翻译后的音频URL")
    @TableField("translated_audio_url")
    private String translatedAudioUrl;

    /**
     * 状态：0-排队中 1-进行中 2-翻译成功 3-失败 4-超时 5-已取消
     */
    @Schema(description = "状态：0-排队中 1-进行中 2-翻译成功 3-失败 4-超时 5-已取消")
    @TableField("status")
    private Integer status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 翻译视频的时长 (毫秒)
     */
    @Schema(description = "翻译视频的时长 (毫秒)")
    @TableField("duration_ms")
    private Integer durationMs;

    /**
     * 视频翻译请求参数 (JSON格式，包含Azure特定字段)
     */
    @Schema(description = "视频翻译请求参数 (JSON格式，包含Azure特定字段)")
    @TableField("request_params_json")
    private String requestParamsJson;

    /**
     * 翻译结果详情 (JSON格式)
     */
    @Schema(description = "翻译结果详情 (JSON格式)")
    @TableField("result_json")
    private String resultJson;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    @Schema(description = "是否删除：0-未删除 1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    // ==================== 业务方法 ====================

    /**
     * 检查任务是否处理中
     */
    public boolean isProcessing() {
        return status != null && (status == 0 || status == 1);
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return status != null && status == 2;
    }

    /**
     * 检查任务是否失败
     */
    public boolean isFailed() {
        return status != null && (status == 3 || status == 4);
    }

    /**
     * 检查任务是否已取消
     */
    public boolean isCancelled() {
        return status != null && status == 5;
    }

    /**
     * 检查任务是否为最终状态
     */
    public boolean isFinalStatus() {
        return isCompleted() || isFailed() || isCancelled();
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 0: return "排队中";
            case 1: return "进行中";
            case 2: return "翻译成功";
            case 3: return "失败";
            case 4: return "超时";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 检查是否有结果文件
     */
    public boolean hasResultFiles() {
        return (translatedVideoUrl != null && !translatedVideoUrl.trim().isEmpty()) ||
               (translatedAudioUrl != null && !translatedAudioUrl.trim().isEmpty());
    }

    /**
     * 检查是否有视频结果
     */
    public boolean hasVideoResult() {
        return translatedVideoUrl != null && !translatedVideoUrl.trim().isEmpty();
    }

    /**
     * 检查是否有音频结果
     */
    public boolean hasAudioResult() {
        return translatedAudioUrl != null && !translatedAudioUrl.trim().isEmpty();
    }

    /**
     * 获取任务摘要信息
     */
    public String getTaskSummary() {
        return String.format("任务[%s]: %s, 状态: %s, 服务商: %s",
            taskId,
            taskName != null ? taskName : "视频翻译",
            getStatusDescription(),
            provider != null ? provider : "未知");
    }

    /**
     * 获取时长描述（毫秒转换为可读格式）
     */
    public String getDurationDescription() {
        if (durationMs == null || durationMs <= 0) {
            return "未知";
        }

        long totalSeconds = durationMs / 1000;
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;

        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }
}
