package com.nacos.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 视频文件验证工具类
 * 提供统一的视频文件验证功能，包括格式检查、大小验证、MIME类型验证等
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
public class VideoFileValidator {

    /**
     * 支持的视频文件格式（扩展名）
     */
    private static final String[] ALLOWED_VIDEO_TYPES = {".mp4", ".mov", ".avi"};
    
    /**
     * 支持的MIME类型
     */
    private static final String[] ALLOWED_MIME_TYPES = {
        "video/mp4",
        "video/quicktime", 
        "video/x-msvideo"
    };
    
    /**
     * 最大文件大小：300MB（符合nacos-digital-server配置）
     */
    private static final long MAX_FILE_SIZE = 300 * 1024 * 1024L;
    
    /**
     * 验证结果类
     */
    @Data
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private String fileName;
        private long fileSize;
        private String fileExtension;
        private String mimeType;
        
        public static ValidationResult success(String fileName, long fileSize, String fileExtension, String mimeType) {
            ValidationResult result = new ValidationResult();
            result.valid = true;
            result.fileName = fileName;
            result.fileSize = fileSize;
            result.fileExtension = fileExtension;
            result.mimeType = mimeType;
            return result;
        }
        
        public static ValidationResult error(String errorMessage) {
            ValidationResult result = new ValidationResult();
            result.valid = false;
            result.errorMessage = errorMessage;
            return result;
        }
    }
    
    /**
     * 验证视频文件
     * 
     * @param file 待验证的文件
     * @return 验证结果
     */
    public static ValidationResult validateVideoFile(MultipartFile file) {
        String methodName = "validateVideoFile";
        
        try {
            // 1. 基础验证：文件不能为空
            if (file == null || file.isEmpty()) {
                log.warn("[{}] 文件验证失败: 文件为空", methodName);
                return ValidationResult.error("文件不能为空");
            }
            
            // 2. 文件名验证
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                log.warn("[{}] 文件验证失败: 文件名为空", methodName);
                return ValidationResult.error("文件名不能为空");
            }
            
            // 3. 文件扩展名验证
            if (!originalFilename.contains(".")) {
                log.warn("[{}] 文件验证失败: 文件没有扩展名, fileName={}", methodName, originalFilename);
                return ValidationResult.error("文件必须包含扩展名");
            }
            
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
            if (!isValidFileExtension(fileExtension)) {
                log.warn("[{}] 文件验证失败: 不支持的文件格式, fileName={}, extension={}", 
                    methodName, originalFilename, fileExtension);
                return ValidationResult.error("不支持的文件格式，仅支持: " + String.join(", ", ALLOWED_VIDEO_TYPES));
            }
            
            // 4. 文件大小验证
            long fileSize = file.getSize();
            if (fileSize > MAX_FILE_SIZE) {
                log.warn("[{}] 文件验证失败: 文件大小超限, fileName={}, size={}MB, limit={}MB", 
                    methodName, originalFilename, fileSize / 1024 / 1024, MAX_FILE_SIZE / 1024 / 1024);
                return ValidationResult.error("文件大小超过限制，最大支持: " + (MAX_FILE_SIZE / 1024 / 1024) + "MB");
            }
            
            // 5. MIME类型验证（可选，因为有些客户端可能不设置正确的MIME类型）
            String mimeType = file.getContentType();
            if (mimeType != null && !isValidMimeType(mimeType)) {
                log.warn("[{}] 文件验证警告: MIME类型不匹配, fileName={}, mimeType={}", 
                    methodName, originalFilename, mimeType);
                // 注意：这里只记录警告，不阻止上传，因为主要依据文件扩展名
            }
            
            log.info("[{}] 文件验证成功: fileName={}, size={}MB, extension={}, mimeType={}", 
                methodName, originalFilename, fileSize / 1024 / 1024, fileExtension, mimeType);
            
            return ValidationResult.success(originalFilename, fileSize, fileExtension, mimeType);
            
        } catch (Exception e) {
            log.error("[{}] 文件验证异常: error={}", methodName, e.getMessage(), e);
            return ValidationResult.error("文件验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查文件扩展名是否有效
     * 
     * @param fileExtension 文件扩展名（包含点号，如.mp4）
     * @return 是否有效
     */
    private static boolean isValidFileExtension(String fileExtension) {
        if (fileExtension == null) {
            return false;
        }
        return Arrays.asList(ALLOWED_VIDEO_TYPES).contains(fileExtension.toLowerCase());
    }
    
    /**
     * 检查MIME类型是否有效
     * 
     * @param mimeType MIME类型
     * @return 是否有效
     */
    private static boolean isValidMimeType(String mimeType) {
        if (mimeType == null) {
            return false;
        }
        return Arrays.asList(ALLOWED_MIME_TYPES).contains(mimeType.toLowerCase());
    }
    
    /**
     * 获取支持的文件格式列表
     * 
     * @return 支持的文件格式
     */
    public static List<String> getSupportedVideoTypes() {
        return Arrays.asList(ALLOWED_VIDEO_TYPES);
    }
    
    /**
     * 获取支持的MIME类型列表
     * 
     * @return 支持的MIME类型
     */
    public static List<String> getSupportedMimeTypes() {
        return Arrays.asList(ALLOWED_MIME_TYPES);
    }
    
    /**
     * 获取最大文件大小（字节）
     * 
     * @return 最大文件大小
     */
    public static long getMaxFileSize() {
        return MAX_FILE_SIZE;
    }
    
    /**
     * 获取最大文件大小（MB）
     * 
     * @return 最大文件大小（MB）
     */
    public static long getMaxFileSizeMB() {
        return MAX_FILE_SIZE / 1024 / 1024;
    }
    
    /**
     * 格式化文件大小为可读字符串
     * 
     * @param sizeInBytes 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.1f KB", sizeInBytes / 1024.0);
        } else {
            return String.format("%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        }
    }
}
